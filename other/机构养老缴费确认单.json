[
  {
    "type": "bedFee",
    "typeName": "床位费(单人间)",
    "feeStandard": 3000,
    "prepaidPeriod": {
      "full": 0,
      "partial": 0,
      "discount": 0.5,
      "daysInMonth": 28 // 一个月的天数
    },
    "paymentAmount": 2000,
    "changePaymentId": "cg123", // 关联表：t_payment_change_record
    "estimatedExpiryDate": "2024-10-01",
    "remarks": "",
  },
  {
    "type": "beddingFee ",
    "typeName": "床品费",
    "feeStandard": 500,
    "paymentAmount": 500,
    "remarks": "",
  },
  {
    "type": "careFee",
    "typeName": "护理费(全护1级)",
    "feeStandard": 2500,
    "leavePeriod": {
      "full": 0,
      "partial": 0
    },
    "refundAmount": 0,
    "prepaidPeriod": {
      "full": 0,
      "partial": 0,
      "daysInMonth": 28 // 一个月的天数
    },
    "paymentAmount": 0,
    "changePaymentId": "cg123", // 关联表：t_payment_change_record
    "estimatedExpiryDate": "2024-09-01",
    "remarks": "",
  },
  {
    "type": "mealFee",
    "typeName": "餐费(标准)",
    "feeStandard": 800,
    "leavePeriod": {
      "full": 0,
      "partial": 0
    },
    "refundAmount": 0,
    "prepaidPeriod": {
      "full": 0,
      "partial": 0,
      "daysInMonth": 28 // 一个月的天数
    },
    "paymentAmount": 0,
    "changePaymentId": "cg123", // 关联表：t_payment_change_record
    "estimatedExpiryDate": "2024-10-11",
    "remarks": "",
  },
  {
    "type": "acFee",
    "typeName": "空调费",
    "feeStandard": 500,
    "leavePeriod": {
      "full": 0,
      "partial": 0
    },
    "refundAmount": 0,
    "paymentAmount": 0,
    "dateRange": "2025/6/1~2025/9/15",
    "estimatedExpiryDate": "2024-08-01",
    "remarks": "",
  },
  {
    "type": "addedService",
    "typeName": "增值服务(3)",
    "billIds": "1,3,5", // 本次缴费的增值服务账单ID，多个逗号隔开
    "paymentAmount": 666,
    "remarks": ""
  },
  {
    "type": "medicalSecurityFee",
    "typeName": "医疗保险金",
    "feeStandard": 0,  // 展示确认单此处累加金额，初始为0
    "paymentAmount": 0,
    "estimatedExpiryDate": "",
    "remarks": "",
  },
  {
    "type": "accountDeduction",
    "typeName": "消费账户抵扣",
    "feeStandard": 0, // 展示账户余额
    "paymentAmount": 0,
    "estimatedExpiryDate": "",
    "remarks": "",
  }
]
