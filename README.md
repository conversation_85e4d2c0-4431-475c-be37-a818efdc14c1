# **1. 概述**
## **1.1 项目背景**
+ **项目名称**：驻马店养老系统  
+ **技术栈**：Spring Boot、Spring Cloud Alibaba、Nacos、Redis、MySQL、Vue 前端  
+ **部署目标**：生产环境（测试/开发环境）

## **1.2 系统架构概述**
+ **核心模块**：  
    - `ruoyi-gateway`：API 网关（端口 8080）  
    - `ruoyi-auth`：认证中心（端口 9200）  
    - `ruoyi-system`：系统模块（用户、角色、部门管理等，端口 9201）  
    - `ruoyi-gen`：代码生成器（端口 9202）  
    - `ruoyi-job`：定时任务（端口 9203）  
    - `ruoyi-file`：文件服务（端口 9300）  
    - `ruoyi-visual-monitor`：监控中心（端口 9100）
+ **业务模块**：  
    - `ruoyi-assets`：资产管理系统（端口 9602）  
    - `ruoyi-custom`：机构养老系统（端口 9204）  
    - `ruoyi-realty`：物业管理系统（端口 9605）  
    - `ruoyi-security`：楼宇管理系统（端口 9701）
+ **依赖组件**：  
    - **Nacos**：配置中心与注册中心  
    - **MySQL**：主数据库，存储业务数据  
    - **Redis**：缓存

---

# **2. 环境准备**
## **2.1 硬件与网络要求**
+ **服务器配置**：8 核 CPU、16GB 内存、200GB 存储
+ **网络**：开放端口（如 8080、9200、9201 等）

## **2.2 软件依赖**
+ **操作系统**：CentOS 7
+ **JDK**：JDK 1.8（手动下载或通过宝塔安装）
+ **数据库**：
    - **MySQL**：8.0.24（通过宝塔安装并配置）
    - **Redis**：7.4（通过宝塔安装并配置）
+ **中间件**：
    - **Nacos**：手动部署（需配置指向宝塔的 MySQL）
    - **Nginx**：通过宝塔安装并配置反向代理
+ **开发工具**：
    - **Maven**：3.0+（项目构建）
    - **Node.js**：12+（前端构建）

---

# **3. 部署流程**
## **3.1 安装依赖组件**
### **3.1.1 安装 MySQL**
1. 登录宝塔面板，点击 **软件商店** → 搜索并安装 **MySQL**。
2. 安装完成后，进入 **数据库** → **创建数据库**：
    - 数据库名：`ry-cloud`、`ry-config`、`ry-seata`、`ry-custom`、`ry-assets`、`ry-security`、`ry-gw`、`ry-realty`
    - 字符集：`utf8mb4`
    - 记录密码
3. 导入 SQL 脚本：
    - 通过宝塔 **数据库管理** → **导入**，上传并执行 `ry-cloud.sql`、`ry-config.sql`、`ry-security.sql`、`ry-custom.sql` 等脚本。

### **3.1.2 安装 Redis**
1. 登录宝塔面板，点击 **软件商店** → 搜索并安装 **Redis**。
2. 进入 **Redis** 管理界面：
    - 设置密码（如 `your_redis_password`）
    - 绑定 IP 为 `0.0.0.0`（允许远程访问，生产环境不建议）
    - 保存配置并重启服务

### **3.1.3 部署 Nacos**
1. 下载 Nacos 单机版至服务器（如 `/www/wwwroot/nacos`）。
2. 修改 `application.properties`：

```properties
# 配置宝塔 MySQL 的连接
db.num=1
db.url.0=***************************************************************************************************************************************************************
db.user=ry-config
db.password=宝塔MySQL密码
```

3. 启动 Nacos：

```bash
sh startup.sh -m standalone
```

### **3.1.4 配置防火墙与端口**
+ 在宝塔 **安全** → **放行端口** 中添加：
    - 3306（MySQL）、6379（Redis）、8848（Nacos）、8080（网关）、9200（认证中心）等

## **3.2 部署后端服务**
### **3.2.1 下载并解压代码**
```bash
git clone http://**************/gitlab/yanglao-backend/zmd-elderly-servcie.git
```

### **3.2.2 修改各模块配置文件**
+ 示例：修改 `ruoyi-modules/ruoyi-custom/src/main/resources/bootstrap.yml`，配置 Nacos 地址（如 `127.0.0.1:8848`）。
+ `ruoyi-gateway` **示例配置**：

```yaml
server:
  port: 9204
spring:
  application:
    name: ruoyi-custom
  profiles:
    active: local  # 使用 local 环境配置
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848  # Nacos 服务注册地址
        namespace: ${spring.profiles.active}  # 命名空间根据环境动态选择（如 local）
      config:
        server-addr: 127.0.0.1:8848  # Nacos 配置中心地址
        file-extension: yml  # 配置文件格式
        shared-configs:  # 共享配置文件
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: ${spring.profiles.active}  # 配置中心命名空间与环境一致
```

### **3.2.3 构建并启动服务**
1. 进入各模块目录，执行 Maven 构建：

```bash
cd ruoyi-system
mvn clean package -Dmaven.test.skip=true
```

2. 通过宝塔 **文件** 功能上传 JAR 包至指定目录（如 `/www/wwwroot/ruoyi-system`）。
3. 使用宝塔 **网站** → **Java 项目** → **批量操作** 启动服务。
![img.png](other/img.png)
## **3.3 部署前端**
1. 进入 `project-name` 目录：

```bash
cd project-name
```

2. 安装依赖并构建：

```bash
npm install
npm run build
```

3. 通过宝塔 **文件** 功能上传 `dist` 目录至站点根目录（如 `/www/server/nginx/html/project_name`）。
![img_1.png](other/img_1.png)
5. **注意事项**：
    - **解决缺少 **`vxe-table`** 依赖的问题**：
        1. 终端输入：

```bash
npm install --save vxe-table/lib/vxe-table
```

        2. 在 `package.json` 中修改 `"babel-plugin-import": "1.13.6"`。  
        3. 终端输入：  

```bash
npm install vxe-table@3.5.9
```

    - **中控菜单**：  
中控跳转菜单为固定跳转新页面，在 `src/views/controlPlatform.vue` 页面中进行配置。  
- **打包配置**：  
除 APP 外，所有项目打包均需在 `vue.config.js` 中修改对应地址，`.env` 文件作为修改请求路径环境后缀。

## **3.4 配置反向代理（Nginx）**
1. 登录宝塔面板，点击 **Nginx** → **配置修改**。
2. 添加配置规则：

```nginx
# 机构养老
server {
    listen       23407;
    server_name  localhost;
    charset utf-8;

    location / {
        root   /www/server/nginx/html/custom;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }

    location /test-api/ {
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://localhost:8080/;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
```

3. 保存并重启 Nginx。
4. 完整配置示例见附件：[nginx_sample.conf](other/nginx_sample.conf)。

---

# **4. 配置管理**
## **4.1 通过 Nacos 控制台修改配置**
### **4.1.1 登录 Nacos 控制台**
1. 访问 Nacos 管理界面：

```plain
http://<Nacos服务器IP>:8848/nacos
```

    - 默认用户名和密码：`nacos`（如已修改，使用实际凭据）。
2. 进入 **配置管理** → **配置列表**。

### **4.1.2 修改 **`ruoyi-custom`** 模块配置**
#### **步骤 1：定位配置文件**
1. 在 **配置列表** 中筛选 `ruoyi-custom` 配置：
    - **数据ID**：`ruoyi-custom-test.yml` 或 `ruoyi-assets-test.yml`（以实际命名规则为准）
    - **组（GROUP）**：`DEFAULT_GROUP`
    - **命名空间**：选择对应环境（如 `test`）

#### **步骤 2：编辑配置内容**
1. 点击 **编辑**，修改以下关键参数：
    - **MySQL 数据库配置**：

```yaml
spring:
  datasource:
    dynamic:
      druid:
        datasource:
          master:
            url: jdbc:mysql://<真实MySQL_IP>:<端口>/ry-custom?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true
            username: ry-custom
            password: <实际数据库密码>  # 替换为宝塔 MySQL 的密码
```

    - **Redis 配置**：  

```yaml
spring:
  redis:
    host: <真实Redis_IP>
    port: 6379
    password: <实际Redis密码>  # 替换为宝塔 Redis 的密码
```

    - **微信小程序配置**：  

```yaml
wx:
  appsecret: <实际AppSecret>
  pay:
    appId: <实际AppID>
    mchId: <实际商户号>
    mchKey: <实际商户密钥>
    apiV3Key: <实际APIv3密钥>
    keyPath: classpath:wxcertificate/apiclient_cert.p12
    privateKeyPath: classpath:wxcertificate/apiclient_key.pem
    privateCertPath: classpath:wxcertificate/apiclient_cert.pem
```

#### **步骤 3：发布配置**
1. 点击 **发布**，配置生效（通常 30 秒内）。
2. 系统自动同步至订阅该配置的 `ruoyi-custom` 实例。
    - **环境隔离**：通过 **命名空间** 或 **组（GROUP）** 管理多环境配置（如 `prod`、`test`）。

---

# **5. 测试与验证**
## **5.1 后端接口测试**
+ 使用 Postman 测试核心接口（如 `/auth/login`）。

## **5.2 前端访问测试**
+ 访问 `http://your_domain`，使用默认账号登录。

## **5.3 服务健康检查**
+ 检查宝塔 **软件商店** 中各服务状态（MySQL、Redis、Nginx）。

---

# **6. 维护与监控**
## **6.1 日志管理**
+ 日志路径：各模块 JAR 包目录下的 `logs` 文件夹（通过宝塔 **文件** 查看）。

## **6.2 监控与告警**
+ 使用宝塔 **监控** 功能查看 CPU、内存、磁盘使用情况。

## **6.3 服务更新与回滚**
+ 通过宝塔 **文件** 替换 JAR 包并重启服务。

---

# **7. 附录**
## **7.1 常见问题（FAQ）**
+ **问题 1**：Redis 无法连接？
    - 检查宝塔 Redis 管理界面的密码和绑定 IP 设置。
+ **问题 2**：Nginx 反向代理失败？
    - 确保宝塔已放行相关端口，并检查配置语法。

## **7.2 参考文档**
+ 宝塔面板官方文档：[https://www.bt.cn/](https://www.bt.cn/)
+ 原 RuoYi-Cloud 官方文档：[http://doc.ruoyi.vip/ruoyi-cloud/](http://doc.ruoyi.vip/ruoyi-cloud/)

---

