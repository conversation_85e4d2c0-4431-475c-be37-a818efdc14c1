---
inclusion: always
---

# Ruoyi-Custom 代码规范和开发指南

## 项目架构

### 包结构规范
- 根包：`com.ruoyi.custom`
- 子模块包：`com.ruoyi.custom.{模块名}`
- 标准分层结构：
  - `controller`: REST控制器层
  - `domain`: 实体类（对应数据库表）
  - `mapper`: 数据访问层接口
  - `service`: 业务服务接口
  - `service.impl`: 业务服务实现
  - `api`: 对外API接口（如adminApp、miniApp等）
  - `resp`: 响应对象（用于复杂查询返回）

### 命名约定

**类命名**
- Controller: `{业务名称}Controller`
- Service接口: `{业务名称}Service`
- Service实现: `{业务名称}ServiceImpl`
- Mapper接口: `{业务名称}Mapper`
- 实体类: 业务名称，如 `EmployeeInfo`

**方法命名**
- 查询单条: `select{业务名称}ById`
- 查询列表: `select{业务名称}List`
- 新增: `insert{业务名称}`
- 修改: `update{业务名称}`
- 删除单条: `delete{业务名称}ById`
- 批量删除: `delete{业务名称}ByIds`
- 统计查询: `count{业务名称}`, `stat{业务名称}`
- 导出功能: `export{业务名称}`
- 业务操作: `change{业务名称}`, `assign{业务名称}`

## 代码风格

### 格式规范
- 左大括号 `{` 紧跟在函数/类声明后，不换行
- 使用标准Java缩进和格式

### 注释规范
**类注释模板**
```java
/**
 * {类描述}
 *
 * <AUTHOR>
 * @date {当前日期 yyyy-MM-dd}
 */
```

**方法注释模板**
```java
/**
 * {方法描述}
 *
 * @param {参数名} {参数描述}
 * @return {返回值描述}
 */
```

## REST API 规范

### 请求映射
- GET: 获取资源
- POST: 创建资源
- PUT: 更新资源
- DELETE: 删除资源

### 权限控制
权限注解以注释形式存在，不实际启用：
```java
// @RequiresPermissions("custom:order:assign")
@Log(title = "服务工单", businessType = BusinessType.UPDATE)
@PutMapping("/assign/{id}")
public AjaxResult assignHandler(@RequestBody ServiceOrder serviceOrder) {
    return toAjax(serviceOrderService.changeHandler(serviceOrder));
}
```
权限标识格式：`{模块名}:{业务名}:{操作}`

### Swagger文档
**控制器注解**
```java
@Api(value = "{控制器标识}", tags = "{控制器标签}")
```

**方法注解**
```java
@ApiOperation(value = "{操作描述}")
```

**查询参数文档**
查询、导出方法必须明确标识所有查询参数：
```java
@ApiImplicitParams({
    @ApiImplicitParam(name = "name", value = "名称", paramType = "query"),
    @ApiImplicitParam(name = "status", value = "状态", paramType = "query"),
    @ApiImplicitParam(name = "params.beginTime", value = "开始时间", paramType = "query"),
    @ApiImplicitParam(name = "params.endTime", value = "结束时间", paramType = "query")
})
```

## 实体类规范

### 基本结构
- 继承 `BaseEntity` 类
- 使用 Lombok `@Data` 注解
- 不使用VO/DTO，直接使用domain类

### 注解规范
```java
@Data
@ApiModel(value = "{实体描述}")
public class EntityName extends BaseEntity {
    /**
     * {字段描述}
     */
    @ApiModelProperty(value = "{属性描述}")
    @Excel(name = "{Excel列名}")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") // 日期字段
    private DataType fieldName;
}
```

### 传参规则
- domain类字段：直接使用domain类接收
- 非domain字段：使用BaseEntity的params（Map<String, Object>）
- 路径变量与domain字段重名时，直接使用domain对象值

## 数据库规范

### 表命名
- 业务表以 `t_` 前缀开头
- 使用下划线分隔，全小写
- 名称反映业务含义

### 字段命名
- 主键: `id`
- 外键: `{关联表去掉t_前缀}_id`
- 创建时间: `create_time`
- 更新时间: `update_time`
- 创建者: `create_by`
- 更新者: `update_by`
- 逻辑删除: `del_flag`

### 注释规范
- 表和字段必须添加注释
- 字典字段注释格式：`字段含义，字典：{字典类型}；{字典值含义}`

## 字典管理

### 命名规范
- 业务字典前缀: `custom_`
- 格式: `custom_{表名}_{字段名}`
- 全小写，下划线分隔

### SQL生成
字典字段必须生成对应的字典SQL：
```sql
-- 字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`) 
VALUES ('机构养老-{模块名}-{字段中文名}', '{字典类型}', '0', 'admin', NOW());

-- 字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `status`, `create_by`, `create_time`) 
VALUES ({排序}, '{标签}', '{值}', '{字典类型}', '0', 'admin', NOW());
```

## 异常处理

### 业务异常
- 使用 `ServiceException` 抛出业务异常
- 提供明确的错误信息
- 在Service层处理业务异常，不在Controller层

```java
@Override
@Transactional(rollbackFor = Exception.class)
public int changeHandler(ServiceOrder serviceOrder) {
    ServiceOrder oldOrder = serviceOrderMapper.selectServiceOrderById(serviceOrder.getId());
    if (oldOrder == null) {
        throw new ServiceException("工单不存在，无法更换处理人员");
    }
    return serviceOrderMapper.updateServiceOrder(serviceOrder);
}
```

## 事务和返回值

### 事务控制
- Service实现类方法使用 `@Transactional(rollbackFor = Exception.class)`
- 多表操作必须添加事务控制

### 返回值规范
- 单个操作结果: `AjaxResult`
- 分页列表: `TableDataInfo`
- 使用 `startPage()` 开始分页，`getDataTable()` 获取分页数据

## SQL代码管理

### 管理方式
- **不再使用sql文件夹**：项目中的sql文件夹内容已过时，不再维护和新增
- **临时文本形式**：需要生成SQL代码时，以临时文本形式提供，无需保存到文件
- **无需即时执行**：SQL代码生成后只展示，无需执行

### SQL代码规范
- **DDL语句**：表结构变更（CREATE、ALTER、DROP）
- **DML语句**：数据变更（INSERT、UPDATE、DELETE）
- **注释要求**：每个SQL语句必须添加用途注释
- **执行顺序**：多个SQL语句需要标明执行顺序

### SQL代码示例
```sql
-- 创建业务表
CREATE TABLE `t_example_info` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `status` varchar(2) DEFAULT '0' COMMENT '状态，字典：custom_example_status；0：正常，1：停用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `del_flag` varchar(2) DEFAULT '0' COMMENT '删除标识（0正常，1删除）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例信息表';

-- 创建字典类型
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`) 
VALUES ('机构养老-示例-状态', 'custom_example_status', '0', 'admin', NOW());

-- 创建字典数据
INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `status`, `create_by`, `create_time`) 
VALUES 
(1, '正常', '0', 'custom_example_status', '0', 'admin', NOW()),
(2, '停用', '1', 'custom_example_status', '0', 'admin', NOW());
```

### 执行建议
- 在开发环境先测试SQL语句
- 确认无误后在测试环境执行
- 生产环境执行前做好数据备份
- 记录执行时间和执行人员

## 工具和日志

### 常用工具
- Excel处理: `ExcelUtil`
- 操作日志: `@Log` 注解
- 字符串工具: `StringUtils`
- 日期工具: `DateUtils`
- 数据转换: `Convert`

### MyBatis查询条件示例
```xml
<where>
    <if test="params.beginCallUpTime != null and params.beginCallUpTime != '' and params.endCallUpTime != null and params.endCallUpTime != ''">
        and date_format(call_up_time,'%Y-%m') between #{params.beginCallUpTime} and #{params.endCallUpTime}
    </if>
    <if test="name != null and name != ''">
        and name like concat('%', #{name}, '%')
    </if>
    <if test="status != null and status != ''">
        and status = #{status}
    </if>
</where>
```

## 代码模板和示例

### Controller层标准模板
```java
/**
 * {业务名称}Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Api(value = "{业务标识}", tags = "{业务名称}管理")
@RestController
@RequestMapping("/custom/{模块名}/{业务名}")
public class {业务名称}Controller extends BaseController {
    
    @Autowired
    private I{业务名称}Service {业务名称}Service;

    /**
     * 查询{业务名称}列表
     */
    @ApiOperation(value = "查询{业务名称}列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "名称", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态", paramType = "query"),
        @ApiImplicitParam(name = "params.beginTime", value = "开始时间", paramType = "query"),
        @ApiImplicitParam(name = "params.endTime", value = "结束时间", paramType = "query")
    })
    // @RequiresPermissions("custom:{模块名}:{业务名}:list")
    @GetMapping("/list")
    public TableDataInfo list({业务名称} {业务名称}) {
        startPage();
        List<{业务名称}> list = {业务名称}Service.select{业务名称}List({业务名称});
        return getDataTable(list);
    }

    /**
     * 获取{业务名称}详细信息
     */
    @ApiOperation(value = "获取{业务名称}详细信息")
    // @RequiresPermissions("custom:{模块名}:{业务名}:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success({业务名称}Service.select{业务名称}ById(id));
    }

    /**
     * 新增{业务名称}
     */
    @ApiOperation(value = "新增{业务名称}")
    // @RequiresPermissions("custom:{模块名}:{业务名}:add")
    @Log(title = "{业务名称}", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody {业务名称} {业务名称}) {
        return toAjax({业务名称}Service.insert{业务名称}({业务名称}));
    }

    /**
     * 修改{业务名称}
     */
    @ApiOperation(value = "修改{业务名称}")
    // @RequiresPermissions("custom:{模块名}:{业务名}:edit")
    @Log(title = "{业务名称}", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody {业务名称} {业务名称}) {
        return toAjax({业务名称}Service.update{业务名称}({业务名称}));
    }

    /**
     * 删除{业务名称}
     */
    @ApiOperation(value = "删除{业务名称}")
    // @RequiresPermissions("custom:{模块名}:{业务名}:remove")
    @Log(title = "{业务名称}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax({业务名称}Service.delete{业务名称}ByIds(ids));
    }

    /**
     * 导出{业务名称}列表
     */
    @ApiOperation(value = "导出{业务名称}列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "name", value = "名称", paramType = "query"),
        @ApiImplicitParam(name = "status", value = "状态", paramType = "query")
    })
    // @RequiresPermissions("custom:{模块名}:{业务名}:export")
    @Log(title = "{业务名称}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, {业务名称} {业务名称}) {
        List<{业务名称}> list = {业务名称}Service.select{业务名称}List({业务名称});
        ExcelUtil<{业务名称}> util = new ExcelUtil<{业务名称}>({业务名称}.class);
        util.exportExcel(response, list, "{业务名称}数据");
    }
}
```

### Service层标准模板
```java
/**
 * {业务名称}Service接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface I{业务名称}Service {
    /**
     * 查询{业务名称}
     *
     * @param id {业务名称}主键
     * @return {业务名称}
     */
    public {业务名称} select{业务名称}ById(String id);

    /**
     * 查询{业务名称}列表
     *
     * @param {业务名称} {业务名称}
     * @return {业务名称}集合
     */
    public List<{业务名称}> select{业务名称}List({业务名称} {业务名称});

    /**
     * 新增{业务名称}
     *
     * @param {业务名称} {业务名称}
     * @return 结果
     */
    public int insert{业务名称}({业务名称} {业务名称});

    /**
     * 修改{业务名称}
     *
     * @param {业务名称} {业务名称}
     * @return 结果
     */
    public int update{业务名称}({业务名称} {业务名称});

    /**
     * 批量删除{业务名称}
     *
     * @param ids 需要删除的{业务名称}主键集合
     * @return 结果
     */
    public int delete{业务名称}ByIds(String[] ids);

    /**
     * 删除{业务名称}信息
     *
     * @param id {业务名称}主键
     * @return 结果
     */
    public int delete{业务名称}ById(String id);
}
```

### ServiceImpl层标准模板
```java
/**
 * {业务名称}Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class {业务名称}ServiceImpl implements I{业务名称}Service {
    @Autowired
    private {业务名称}Mapper {业务名称}Mapper;

    /**
     * 查询{业务名称}
     *
     * @param id {业务名称}主键
     * @return {业务名称}
     */
    @Override
    public {业务名称} select{业务名称}ById(String id) {
        return {业务名称}Mapper.select{业务名称}ById(id);
    }

    /**
     * 查询{业务名称}列表
     *
     * @param {业务名称} {业务名称}
     * @return {业务名称}
     */
    @Override
    public List<{业务名称}> select{业务名称}List({业务名称} {业务名称}) {
        return {业务名称}Mapper.select{业务名称}List({业务名称});
    }

    /**
     * 新增{业务名称}
     *
     * @param {业务名称} {业务名称}
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert{业务名称}({业务名称} {业务名称}) {
        // 业务校验
        if (StringUtils.isEmpty({业务名称}.getName())) {
            throw new ServiceException("名称不能为空");
        }
        
        {业务名称}.setId(UUID.randomUUID().toString());
        {业务名称}.setCreateTime(DateUtils.getNowDate());
        return {业务名称}Mapper.insert{业务名称}({业务名称});
    }

    /**
     * 修改{业务名称}
     *
     * @param {业务名称} {业务名称}
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update{业务名称}({业务名称} {业务名称}) {
        // 业务校验
        {业务名称} existing = {业务名称}Mapper.select{业务名称}ById({业务名称}.getId());
        if (existing == null) {
            throw new ServiceException("{业务名称}不存在");
        }
        
        {业务名称}.setUpdateTime(DateUtils.getNowDate());
        return {业务名称}Mapper.update{业务名称}({业务名称});
    }

    /**
     * 批量删除{业务名称}
     *
     * @param ids 需要删除的{业务名称}主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete{业务名称}ByIds(String[] ids) {
        return {业务名称}Mapper.delete{业务名称}ByIds(ids);
    }

    /**
     * 删除{业务名称}信息
     *
     * @param id {业务名称}主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete{业务名称}ById(String id) {
        return {业务名称}Mapper.delete{业务名称}ById(id);
    }
}
```

## 开发最佳实践

### 1. 参数校验
- Controller层进行基础参数校验
- Service层进行业务逻辑校验
- 使用明确的错误提示信息

### 2. 事务管理
- 所有涉及数据修改的Service方法必须添加事务注解
- 使用 `@Transactional(rollbackFor = Exception.class)`
- 避免在Controller层处理事务

### 3. 异常处理
- 统一使用 `ServiceException` 抛出业务异常
- 异常信息要明确、用户友好
- 记录详细的错误日志

### 4. 性能优化
- 合理使用分页查询
- 避免N+1查询问题
- 对频繁查询的字段建立索引
- 使用缓存优化热点数据

### 5. 代码复用
- 提取公共方法到工具类
- 使用继承减少重复代码
- 合理使用设计模式