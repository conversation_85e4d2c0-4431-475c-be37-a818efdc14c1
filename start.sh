#!/bin/sh
export GATEWAY=ruoyi-gateway.jar
export AUTH=ruoyi-auth.jar
export MONITOR=ruoyi-visual-monitor.jar
export GEN=ruoyi-modules-gen.jar
export JOB=ruoyi-modules-job.jar
export FILE=ruoyi-modules-File.jar
export SYSTEM=ruoyi-modules-system.jar
export CUSTOM=ruoyi-modules-custon.jar


export GATEWAY_port=8181
export AUTH_port=9200
export MONITOR_port=9100
export GEN_port=9202
export SYSTEM_port=9201
export JOB_port=9203
export FILE=9300
export CUSTOM=9204
case "$1" in

start)

        ## 启动gateway
        echo "--------开始启动GATEWAY---------------"
        nohup java -jar -Dfile.encoding=utf-8 $GATEWAY &
        GATEWAY_pid=`lsof -i:$GATEWAY_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$GATEWAY_pid" ]
            do
              GATEWAY_pid=`lsof -i:$GATEWAY_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "GATEWAY pid is $GATEWAY_pid"
        echo "---------GATEWAY 启动成功-----------"

        ## 启动auth
        echo "--------开始启动AUTH---------------"
        nohup java -jar -Dfile.encoding=utf-8 $AUTH &
        AUTH_pid=`lsof -i:$AUTH_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$AUTH_pid" ]
            do
              AUTH_pid=`lsof -i:$AUTH_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "AUTH pid is $AUTH_pid"
        echo "---------AUTH 启动成功-----------"

        ## 启动monitor
        echo "--------开始启动MONITOR---------------"
        nohup java -jar -Dfile.encoding=utf-8 $MONITOR &
        MONITOR_pid=`lsof -i:$MONITOR_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$MONITOR_pid" ]
            do
              MONITOR_pid=`lsof -i:$MONITOR_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "MONITOR pid is $MONITOR_pid"
        echo "---------MONITOR 启动成功-----------"

         ## 启动gen
        echo "--------开始启动GEN---------------"
        nohup java -jar -Dfile.encoding=utf-8 $GEN &
        GEN_pid=`lsof -i:$GEN_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$GEN_pid" ]
            do
              GEN_pid=`lsof -i:$GEN_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "GEN pid is $GEN_pid"
        echo "---------GEN 启动成功-----------"

         ## 启动system
        echo "--------开始启动SYSTEM---------------"
        nohup java -jar -Dfile.encoding=utf-8 $SYSTEM &
        SYSTEM_pid=`lsof -i:$SYSTEM_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$SYSTEM_pid" ]
            do
              SYSTEM_pid=`lsof -i:$SYSTEM_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "SYSTEM pid is $SYSTEM_pid"
        echo "---------SYSTEM 启动成功-----------"

         ## 启动job
        echo "--------开始启动JOB---------------"
        nohup java -jar -Dfile.encoding=utf-8 $JOB &
        JOB_pid=`lsof -i:$JOB_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$JOB_pid" ]
            do
              JOB_pid=`lsof -i:$JOB_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "JOB pid is $JOB_pid"
        echo "---------JOB 启动成功-----------"

        ## 启动file
        echo "--------开始启动OP---------------"
        nohup java -jar -Dfile.encoding=utf-8 $FILE &
        OP_pid=`lsof -i:$FILE_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$FILE_pid" ]
            do
              OP_pid=`lsof -i:$FILE_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "OP pid is $FILE_pid"
        echo "---------OP 启动成功-----------"

        ## 启动CUSTOM
        echo "--------CUSTOM---------------"
        nohup java -jar -Dfile.encoding=utf-8 $CUSTOM &
        MC_pid=`lsof -i:$CUSTOM_port|grep "LISTEN"|awk '{print $2}'`
        until [ -n "$CUSTOM_pid" ]
            do
              MC_pid=`lsof -i:$CUSTOM_port|grep "LISTEN"|awk '{print $2}'`
            done
        echo "CUSTOM pid is $CUSTOM_pid"
        echo "---------CUSTOM 启动成功-----------"

        echo "===startAll success==="
        ;;

 stop)
         P_ID=`ps -ef | grep -w $GATEWAY | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===GATEWAY process not exists or stop success"
        else
            kill -9 $P_ID
            echo "GATEWAY killed success"
        fi
         P_ID=`ps -ef | grep -w $AUTH | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===AUTH process not exists or stop success"
        else
            kill -9 $P_ID
            echo "AUTH killed success"
        fi
         P_ID=`ps -ef | grep -w $MONITOR | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===MONITOR process not exists or stop success"
        else
            kill -9 $P_ID
            echo "MONITOR killed success"
        fi
		 P_ID=`ps -ef | grep -w $GEN | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===GEN process not exists or stop success"
        else
            kill -9 $P_ID
            echo "GEN killed success"
        fi
		 P_ID=`ps -ef | grep -w $SYSTEM | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===SYSTEM process not exists or stop success"
        else
            kill -9 $P_ID
            echo "SYSTEM killed success"
        fi
		 P_ID=`ps -ef | grep -w $JOB | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===JOB process not exists or stop success"
        else
            kill -9 $P_ID
            echo "JOB killed success"
        fi
		 P_ID=`ps -ef | grep -w $CUSTOM | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===MC process not exists or stop success"
        else
            kill -9 $P_ID
            echo "MC killed success"
        fi
		 P_ID=`ps -ef | grep -w $FILE | grep -v "grep" | awk '{print $2}'`
        if [ "$P_ID" == "" ]; then
            echo "===OP process not exists or stop success"
        else
            kill -9 $P_ID
            echo "OP killed success"
        fi

        echo "===stop success==="
        ;;

restart)
        $0 stop
        sleep 2
        $0 start
        echo "===restart success==="
        ;;
esac
exit 0
