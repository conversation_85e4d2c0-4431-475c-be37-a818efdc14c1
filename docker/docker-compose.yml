version: '3.8'
services:
  ruoyi-nacos:
    container_name: ruoyi-nacos
    image: nacos/nacos-server
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - ruoyi-mysql
  ruoyi-mysql:
    container_name: ruoyi-mysql
    image: mysql:5.7
    build:
      context: ./mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
      'mysqld',
      '--innodb-buffer-pool-size=80M',
      '--character-set-server=utf8mb4',
      '--collation-server=utf8mb4_unicode_ci',
      '--default-time-zone=+8:00',
      '--lower-case-table-names=1'
    ]
    environment:
      MYSQL_DATABASE: 'ry-cloud'
      MYSQL_ROOT_PASSWORD: password
  ruoyi-redis:
    container_name: ruoyi-redis
    image: redis
    build:
      context: ./redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  ruoyi-nginx:
    container_name: ruoyi-nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/projects/ruoyi-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - ruoyi-gateway
    links:
      - ruoyi-gateway
  ruoyi-gateway:
    container_name: ruoyi-gateway
    build:
      context: ./ruoyi/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - ruoyi-redis
    links:
      - ruoyi-redis
  ruoyi-auth:
    container_name: ruoyi-auth
    build:
      context: ./ruoyi/auth
      dockerfile: dockerfile
    ports:
      - "9200:9200"
    depends_on:
      - ruoyi-redis
    links:
      - ruoyi-redis
  ruoyi-modules-system:
    container_name: ruoyi-modules-system
    build:
      context: ./ruoyi/modules/system
      dockerfile: dockerfile
    ports:
      - "9201:9201"
    depends_on:
      - ruoyi-redis
      - ruoyi-mysql
    links:
      - ruoyi-redis
      - ruoyi-mysql
  ruoyi-modules-gen:
    container_name: ruoyi-modules-gen
    build:
      context: ./ruoyi/modules/gen
      dockerfile: dockerfile
    ports:
      - "9202:9202"
    depends_on:
      - ruoyi-mysql
    links:
      - ruoyi-mysql
  ruoyi-modules-job:
    container_name: ruoyi-modules-job
    build:
      context: ./ruoyi/modules/job
      dockerfile: dockerfile
    ports:
      - "9203:9203"
    depends_on:
      - ruoyi-mysql
    links:
      - ruoyi-mysql
  ruoyi-modules-file:
    container_name: ruoyi-modules-file
    build:
      context: ./ruoyi/modules/file
      dockerfile: dockerfile
    ports:
      - "9300:9300"
    volumes:
      - ./ruoyi/uploadPath:/home/<USER>/uploadPath
  ruoyi-visual-monitor:
    container_name: ruoyi-visual-monitor
    build:
      context: ./ruoyi/visual/monitor
      dockerfile: dockerfile
    ports:
      - "9100:9100"
