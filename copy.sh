#!/bin/sh


mvn clean

mvn package

# copy jar
echo "begin copy ruoyi-gateway "
cp ./ruoyi-gateway/target/ruoyi-gateway.jar ./package

echo "begin copy ruoyi-auth "
cp ./ruoyi-auth/target/ruoyi-auth.jar ./package

echo "begin copy ruoyi-visual "
cp ./ruoyi-visual/ruoyi-monitor/target/ruoyi-visual-monitor.jar  ./package

echo "begin copy ruoyi-modules-system "
cp ./ruoyi-modules/ruoyi-system/target/ruoyi-modules-system.jar ./package

echo "begin copy ruoyi-modules-custom "
cp ./ruoyi-modules/ruoyi-custom/target/ruoyi-modules-custom.jar ./package

echo "begin copy ruoyi-modules-file "
cp ./ruoyi-modules/ruoyi-file/target/ruoyi-modules-file.jar ./package

echo "begin copy ruoyi-modules-job "
cp ./ruoyi-modules/ruoyi-job/target/ruoyi-modules-job.jar ./package

echo "begin copy ruoyi-modules-gen "
cp ./ruoyi-modules/ruoyi-gen/target/ruoyi-modules-gen.jar ./package

echo "begin copy ruoyi-modules-homecare "
cp ./ruoyi-modules/ruoyi-homecare/target/ruoyi-modules-homecare.jar ./package
